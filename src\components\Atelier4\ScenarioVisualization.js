// src/components/Atelier4/ScenarioVisualization.js
import React, { useState, useCallback, useEffect, useRef } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  Panel,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { X, ZoomIn, ZoomOut, Maximize2, RefreshCw, Download, Save, FileText, Eye, Printer, Target } from 'lucide-react';
import { toPng } from 'html-to-image';
import { jsPDF } from 'jspdf';

// Custom node types for scenario visualization
const ScenarioStepNode = ({ data, selected }) => {
  const getPhaseColor = (phase) => {
    switch (phase) {
      case 'CONNAITRE': return '#3B82F6'; // Blue
      case 'RENTRER': return '#EF4444'; // Red
      case 'TROUVER': return '#F59E0B'; // Amber
      case 'EXPLOITER': return '#10B981'; // Green
      default: return '#6B7280'; // Gray
    }
  };

  const phaseColor = getPhaseColor(data.phase);

  return (
    <div
      className={`relative p-2 border rounded-lg shadow-sm w-48 bg-white ${
        selected ? 'border-blue-500' : 'border-gray-300'
      }`}
      style={{ borderColor: selected ? '#3B82F6' : phaseColor }}
    >
      <div className="flex items-center mb-1">
        <div
          className="w-5 h-5 rounded-full flex items-center justify-center text-white text-xs font-bold mr-2 flex-shrink-0"
          style={{ backgroundColor: phaseColor }}
        >
          {data.stepNumber}
        </div>
        {data.phase && (
          <span
            className="px-1 py-0.5 rounded text-xs font-semibold text-white mr-1"
            style={{ backgroundColor: phaseColor }}
          >
            {data.phase}
          </span>
        )}
      </div>

      <div className="text-xs font-semibold text-gray-800 mb-1">
        {data.name || data.technique}
      </div>

      <div className="text-xs text-gray-600 mb-1 line-clamp-2">
        {data.description}
      </div>

      {data.duration && (
        <div className="text-xs text-gray-500">
          ⏱️ {data.duration}
        </div>
      )}
    </div>
  );
};

const ScenarioHeaderNode = ({ data, selected }) => {
  return (
    <div
      className={`relative p-4 border-2 rounded-lg shadow-lg w-80 bg-gradient-to-r from-indigo-50 to-blue-50 ${
        selected ? 'border-blue-500' : 'border-indigo-400'
      }`}
    >
      <div className="flex items-center mb-3">
        <Target className="text-indigo-600 mr-3 flex-shrink-0" size={24} />
        <div className="font-bold text-indigo-800 text-base">{data.name}</div>
      </div>

      <div className="text-sm text-indigo-700 mb-3 line-clamp-3">{data.description}</div>

      <div className="grid grid-cols-2 gap-3 text-xs">
        {data.severity && (
          <div className="bg-white bg-opacity-50 rounded p-2 text-center">
            <div className="text-indigo-600 font-semibold">Sévérité</div>
            <div className="text-indigo-800 font-medium">{data.severity}</div>
          </div>
        )}
        {data.likelihood && (
          <div className="bg-white bg-opacity-50 rounded p-2 text-center">
            <div className="text-indigo-600 font-semibold">Probabilité</div>
            <div className="text-indigo-800 font-medium">{data.likelihood}</div>
          </div>
        )}
        {data.timeline && (
          <div className="bg-white bg-opacity-50 rounded p-2 text-center col-span-2">
            <div className="text-indigo-600 font-semibold">Durée Estimée</div>
            <div className="text-indigo-800 font-medium">{data.timeline}</div>
          </div>
        )}
      </div>
    </div>
  );
};

const nodeTypes = {
  scenarioHeader: ScenarioHeaderNode,
  scenarioStep: ScenarioStepNode,
};

const ScenarioVisualization = ({ scenario, title = "Visualisation du Scénario Opérationnel", inline = false }) => {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);

  const reactFlowWrapper = useRef(null);
  const reactFlowInstance = useReactFlow();

  // Create nodes and edges based on the scenario
  useEffect(() => {
    if (!scenario) return;

    const createScenarioVisualization = () => {
      const newNodes = [];
      const newEdges = [];

      // Create header node (main scenario node)
      const headerNode = {
        id: 'scenario-header',
        type: 'scenarioHeader',
        position: { x: 150, y: 50 },
        data: {
          name: scenario.name || scenario.title || 'Scénario Opérationnel',
          description: scenario.description || '',
          severity: scenario.severity,
          likelihood: scenario.likelihood,
          timeline: scenario.timeline,
          detectionDifficulty: scenario.detectionDifficulty,
        },
      };
      newNodes.push(headerNode);

      // Create step nodes if available
      if (scenario.steps && scenario.steps.length > 0) {
        scenario.steps.forEach((step, index) => {
          const stepNode = {
            id: `step-${index}`,
            type: 'scenarioStep',
            position: {
              x: 50 + (index % 4) * 200,
              y: 200 + Math.floor(index / 4) * 120
            },
            data: {
              stepNumber: step.stepNumber || index + 1,
              phase: step.phase,
              name: step.name,
              technique: step.technique,
              description: step.description,
              duration: step.duration,
              difficulty: step.difficulty,
              techniques: step.techniques,
              indicators: step.indicators,
              tools: step.tools,
            },
          };
          newNodes.push(stepNode);

          // Create edge from header to each step (hierarchical connection)
          const headerEdge = {
            id: `header-edge-${index}`,
            source: 'scenario-header',
            target: `step-${index}`,
            type: 'smoothstep',
            animated: false,
            style: { stroke: '#94A3B8', strokeWidth: 1, strokeDasharray: '5,5' },
          };
          newEdges.push(headerEdge);

          // Create sequential edges between steps (flow progression)
          if (index > 0) {
            const sequentialEdge = {
              id: `seq-edge-${index}`,
              source: `step-${index - 1}`,
              target: `step-${index}`,
              type: 'smoothstep',
              animated: true,
              style: { stroke: '#6366F1', strokeWidth: 2 },
              label: `${step.phase || 'Étape'}`,
              labelStyle: {
                fill: '#6366F1',
                fontWeight: 500,
                fontSize: '10px',
                background: '#ffffff',
                padding: '2px 4px',
                borderRadius: '4px'
              },
            };
            newEdges.push(sequentialEdge);
          } else {
            // First step gets a special "Start" edge
            const startEdge = {
              id: `start-edge-${index}`,
              source: 'scenario-header',
              target: `step-${index}`,
              type: 'smoothstep',
              animated: true,
              style: { stroke: '#10B981', strokeWidth: 3 },
              label: 'Début',
              labelStyle: {
                fill: '#10B981',
                fontWeight: 600,
                fontSize: '11px',
                background: '#ffffff',
                padding: '2px 6px',
                borderRadius: '4px'
              },
            };
            newEdges.push(startEdge);
          }
        });
      } else {
        // Create a simple node if no detailed steps
        const simpleNode = {
          id: 'simple-scenario',
          type: 'scenarioStep',
          position: { x: 200, y: 200 },
          data: {
            stepNumber: 1,
            name: 'Scénario Simplifié',
            description: scenario.description || 'Aucun détail d\'étapes disponible',
            techniques: [],
            indicators: [],
          },
        };
        newNodes.push(simpleNode);

        const edge = {
          id: 'edge-simple',
          source: 'scenario-header',
          target: 'simple-scenario',
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6366F1', strokeWidth: 2 },
          label: 'Scénario',
          labelStyle: { fill: '#6366F1', fontWeight: 500 },
        };
        newEdges.push(edge);
      }

      setNodes(newNodes);
      setEdges(newEdges);
    };

    createScenarioVisualization();
  }, [scenario]);

  const onNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes]
  );

  const onEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );

  const onConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  );

  // Function to reset the view
  const resetView = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 });
    }
  }, [reactFlowInstance]);

  // Function to export as PNG
  const exportAsPng = useCallback(() => {
    if (reactFlowWrapper.current === null) return;

    toPng(reactFlowWrapper.current, {
      backgroundColor: '#fff',
      width: reactFlowWrapper.current.offsetWidth,
      height: reactFlowWrapper.current.offsetHeight,
    })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `scenario-${new Date().toISOString().split('T')[0]}.png`;
        link.href = dataUrl;
        link.click();
      });
  }, []);

  return (
    <div className="scenario-visualization-inline">
      {/* Compact header for inline display */}
      {!inline && (
        <div className="bg-white border-b border-slate-200 p-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-slate-800 flex items-center">
              <Eye size={20} className="mr-2 text-indigo-600" />
              Visualisation du Scénario
            </h3>

            <div className="flex items-center space-x-2">
              <button
                onClick={resetView}
                className="flex items-center px-2 py-1 bg-slate-100 text-slate-700 rounded hover:bg-slate-200 transition duration-200 text-sm"
                title="Centrer la vue"
              >
                <RefreshCw size={14} className="mr-1" />
                Centrer
              </button>

              <button
                onClick={exportAsPng}
                className="flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition duration-200 text-sm"
                title="Exporter en PNG"
              >
                <Download size={14} className="mr-1" />
                PNG
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Flow diagram */}
      <div className="relative" style={{ height: inline ? '400px' : '500px' }}>
        <div
          ref={reactFlowWrapper}
          className="w-full h-full bg-gray-50 rounded-lg border border-gray-200"
        >
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
            proOptions={{ hideAttribution: true }}
          >
            <Controls
              position="top-right"
              showZoom={true}
              showFitView={true}
              showInteractive={false}
            />
            {!inline && (
              <MiniMap
                nodeColor={(n) => {
                  if (n.type === 'scenarioHeader') return '#E0E7FF';
                  if (n.type === 'scenarioStep') return '#F3F4F6';
                  return '#E2E8F0';
                }}
                nodeStrokeWidth={2}
                pannable
                zoomable
                position="bottom-left"
              />
            )}
            <Background color="#e5e7eb" variant="dots" gap={20} size={1} />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
};

export default ScenarioVisualization;

// CSS Styles (add to your global CSS or component styles)
const styles = `
.scenario-visualization-inline {
  position: relative;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.react-flow__node-scenarioHeader {
  background: transparent !important;
  border: none !important;
}

.react-flow__node-scenarioStep {
  background: transparent !important;
  border: none !important;
}

.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge-text {
  font-size: 12px;
  font-weight: 500;
}

.react-flow__controls {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-flow__controls button {
  background: white;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.2s;
}

.react-flow__controls button:hover {
  background: #f8fafc;
  color: #334155;
}

.react-flow__controls button:last-child {
  border-bottom: none;
}

.react-flow__minimap {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
`;

// Inject styles if not already present
if (typeof document !== 'undefined' && !document.getElementById('scenario-visualization-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'scenario-visualization-styles';
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
